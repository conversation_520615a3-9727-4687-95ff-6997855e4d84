// This file is automatically generated by Keystone, do not modify it manually.
// Modify your Keystone config when you want to change this.

datasource sqlite {
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  provider          = "sqlite"
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        String    @id @default(cuid())
  name      String    @default("")
  email     String    @unique @default("")
  password  String
  posts     Post[]    @relation("Post_author")
  createdAt DateTime? @default(now())
}

model Post {
  id       String  @id @default(cuid())
  title    String  @default("")
  content  String  @default("[{\"type\":\"paragraph\",\"children\":[{\"text\":\"\"}]}]")
  author   User?   @relation("Post_author", fields: [authorId], references: [id])
  authorId String? @map("author")
  tags     Tag[]   @relation("Post_tags")

  @@index([authorId])
}

model Tag {
  id    String @id @default(cuid())
  name  String @default("")
  posts Post[] @relation("Post_tags")
}
