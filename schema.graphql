# This file is automatically generated by Keystone, do not modify it manually.
# Modify your Keystone config when you want to change this.

type User {
  id: ID!
  name: String
  email: String
  password: PasswordState
  posts(where: PostWhereInput! = {}, orderBy: [PostOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PostWhereUniqueInput): [Post!]
  postsCount(where: PostWhereInput! = {}): Int
  createdAt: DateTime
}

type PasswordState {
  isSet: Boolean!
}

scalar DateTime @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input UserWhereUniqueInput {
  id: ID
  email: String
}

input UserWhereInput {
  AND: [UserWhereInput!]
  OR: [UserWhereInput!]
  NOT: [UserWhereInput!]
  id: IDFilter
  name: StringFilter
  email: StringFilter
  posts: PostManyRelationFilter
  createdAt: DateTimeNullableFilter
}

input IDFilter {
  equals: ID
  in: [ID!]
  notIn: [ID!]
  lt: ID
  lte: ID
  gt: ID
  gte: ID
  not: IDFilter
}

input StringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input NestedStringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input PostManyRelationFilter {
  every: PostWhereInput
  some: PostWhereInput
  none: PostWhereInput
}

input DateTimeNullableFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeNullableFilter
}

input UserOrderByInput {
  id: OrderDirection
  name: OrderDirection
  email: OrderDirection
  createdAt: OrderDirection
}

enum OrderDirection {
  asc
  desc
}

input UserUpdateInput {
  name: String
  email: String
  password: String
  posts: PostRelateToManyForUpdateInput
  createdAt: DateTime
}

input PostRelateToManyForUpdateInput {
  disconnect: [PostWhereUniqueInput!]
  set: [PostWhereUniqueInput!]
  create: [PostCreateInput!]
  connect: [PostWhereUniqueInput!]
}

input UserUpdateArgs {
  where: UserWhereUniqueInput!
  data: UserUpdateInput!
}

input UserCreateInput {
  name: String
  email: String
  password: String
  posts: PostRelateToManyForCreateInput
  createdAt: DateTime
}

input PostRelateToManyForCreateInput {
  create: [PostCreateInput!]
  connect: [PostWhereUniqueInput!]
}

type Post {
  id: ID!
  title: String
  content: Post_content_Document
  author: User
  tags(where: TagWhereInput! = {}, orderBy: [TagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TagWhereUniqueInput): [Tag!]
  tagsCount(where: TagWhereInput! = {}): Int
}

type Post_content_Document {
  document(hydrateRelationships: Boolean! = false): JSON!
}

input PostWhereUniqueInput {
  id: ID
}

input PostWhereInput {
  AND: [PostWhereInput!]
  OR: [PostWhereInput!]
  NOT: [PostWhereInput!]
  id: IDFilter
  title: StringFilter
  author: UserWhereInput
  tags: TagManyRelationFilter
}

input TagManyRelationFilter {
  every: TagWhereInput
  some: TagWhereInput
  none: TagWhereInput
}

input PostOrderByInput {
  id: OrderDirection
  title: OrderDirection
}

input PostUpdateInput {
  title: String
  content: JSON
  author: UserRelateToOneForUpdateInput
  tags: TagRelateToManyForUpdateInput
}

input UserRelateToOneForUpdateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
  disconnect: Boolean
}

input TagRelateToManyForUpdateInput {
  disconnect: [TagWhereUniqueInput!]
  set: [TagWhereUniqueInput!]
  create: [TagCreateInput!]
  connect: [TagWhereUniqueInput!]
}

input PostUpdateArgs {
  where: PostWhereUniqueInput!
  data: PostUpdateInput!
}

input PostCreateInput {
  title: String
  content: JSON
  author: UserRelateToOneForCreateInput
  tags: TagRelateToManyForCreateInput
}

input UserRelateToOneForCreateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
}

input TagRelateToManyForCreateInput {
  create: [TagCreateInput!]
  connect: [TagWhereUniqueInput!]
}

type Tag {
  id: ID!
  name: String
  posts(where: PostWhereInput! = {}, orderBy: [PostOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PostWhereUniqueInput): [Post!]
  postsCount(where: PostWhereInput! = {}): Int
}

input TagWhereUniqueInput {
  id: ID
}

input TagWhereInput {
  AND: [TagWhereInput!]
  OR: [TagWhereInput!]
  NOT: [TagWhereInput!]
  id: IDFilter
  name: StringFilter
  posts: PostManyRelationFilter
}

input TagOrderByInput {
  id: OrderDirection
  name: OrderDirection
}

input TagUpdateInput {
  name: String
  posts: PostRelateToManyForUpdateInput
}

input TagUpdateArgs {
  where: TagWhereUniqueInput!
  data: TagUpdateInput!
}

input TagCreateInput {
  name: String
  posts: PostRelateToManyForCreateInput
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Mutation {
  createUser(data: UserCreateInput!): User
  createUsers(data: [UserCreateInput!]!): [User]
  updateUser(where: UserWhereUniqueInput!, data: UserUpdateInput!): User
  updateUsers(data: [UserUpdateArgs!]!): [User]
  deleteUser(where: UserWhereUniqueInput!): User
  deleteUsers(where: [UserWhereUniqueInput!]!): [User]
  createPost(data: PostCreateInput!): Post
  createPosts(data: [PostCreateInput!]!): [Post]
  updatePost(where: PostWhereUniqueInput!, data: PostUpdateInput!): Post
  updatePosts(data: [PostUpdateArgs!]!): [Post]
  deletePost(where: PostWhereUniqueInput!): Post
  deletePosts(where: [PostWhereUniqueInput!]!): [Post]
  createTag(data: TagCreateInput!): Tag
  createTags(data: [TagCreateInput!]!): [Tag]
  updateTag(where: TagWhereUniqueInput!, data: TagUpdateInput!): Tag
  updateTags(data: [TagUpdateArgs!]!): [Tag]
  deleteTag(where: TagWhereUniqueInput!): Tag
  deleteTags(where: [TagWhereUniqueInput!]!): [Tag]
  endSession: Boolean!
  authenticateUserWithPassword(email: String!, password: String!): UserAuthenticationWithPasswordResult
  createInitialUser(data: CreateInitialUserInput!): UserAuthenticationWithPasswordSuccess!
}

union UserAuthenticationWithPasswordResult = UserAuthenticationWithPasswordSuccess | UserAuthenticationWithPasswordFailure

type UserAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type UserAuthenticationWithPasswordFailure {
  message: String!
}

input CreateInitialUserInput {
  name: String
  email: String
  password: String
}

type Query {
  user(where: UserWhereUniqueInput!): User
  users(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  usersCount(where: UserWhereInput! = {}): Int
  post(where: PostWhereUniqueInput!): Post
  posts(where: PostWhereInput! = {}, orderBy: [PostOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PostWhereUniqueInput): [Post!]
  postsCount(where: PostWhereInput! = {}): Int
  tag(where: TagWhereUniqueInput!): Tag
  tags(where: TagWhereInput! = {}, orderBy: [TagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TagWhereUniqueInput): [Tag!]
  tagsCount(where: TagWhereInput! = {}): Int
  keystone: KeystoneMeta!
  authenticatedItem: AuthenticatedItem
}

union AuthenticatedItem = User

type KeystoneMeta {
  adminMeta: KeystoneAdminMeta!
}

type KeystoneAdminMeta {
  lists: [KeystoneAdminUIListMeta!]!
  list(key: String!): KeystoneAdminUIListMeta
}

type KeystoneAdminUIListMeta {
  key: String!
  path: String!
  label: String!
  singular: String!
  plural: String!
  description: String
  pageSize: Int!
  labelField: String!
  fields: [KeystoneAdminUIFieldMeta!]!
  groups: [KeystoneAdminUIFieldGroupMeta!]!
  graphql: KeystoneAdminUIGraphQL!
  initialColumns: [String!]!
  initialSearchFields: [String!]!
  initialSort: KeystoneAdminUISort
  isSingleton: Boolean!
  hideCreate: Boolean!
  hideDelete: Boolean!
  isHidden: Boolean!
  itemQueryName: String!
  listQueryName: String!
}

type KeystoneAdminUIFieldMeta {
  path: String!
  label: String!
  description: String
  isOrderable: Boolean!
  isFilterable: Boolean!
  isNonNull: [KeystoneAdminUIFieldMetaIsNonNull!]
  fieldMeta: JSON
  viewsIndex: Int!
  customViewsIndex: Int
  createView: KeystoneAdminUIFieldMetaCreateView!
  listView: KeystoneAdminUIFieldMetaListView!
  itemView(id: ID): KeystoneAdminUIFieldMetaItemView
  search: QueryMode
}

enum KeystoneAdminUIFieldMetaIsNonNull {
  read
  create
  update
}

type KeystoneAdminUIFieldMetaCreateView {
  fieldMode: KeystoneAdminUIFieldMetaCreateViewFieldMode!
}

enum KeystoneAdminUIFieldMetaCreateViewFieldMode {
  edit
  hidden
}

type KeystoneAdminUIFieldMetaListView {
  fieldMode: KeystoneAdminUIFieldMetaListViewFieldMode!
}

enum KeystoneAdminUIFieldMetaListViewFieldMode {
  read
  hidden
}

type KeystoneAdminUIFieldMetaItemView {
  fieldMode: KeystoneAdminUIFieldMetaItemViewFieldMode
  fieldPosition: KeystoneAdminUIFieldMetaItemViewFieldPosition
}

enum KeystoneAdminUIFieldMetaItemViewFieldMode {
  edit
  read
  hidden
}

enum KeystoneAdminUIFieldMetaItemViewFieldPosition {
  form
  sidebar
}

enum QueryMode {
  default
  insensitive
}

type KeystoneAdminUIFieldGroupMeta {
  label: String!
  description: String
  fields: [KeystoneAdminUIFieldMeta!]!
}

type KeystoneAdminUIGraphQL {
  names: KeystoneAdminUIGraphQLNames!
}

type KeystoneAdminUIGraphQLNames {
  outputTypeName: String!
  whereInputName: String!
  whereUniqueInputName: String!
  createInputName: String!
  createMutationName: String!
  createManyMutationName: String!
  relateToOneForCreateInputName: String!
  relateToManyForCreateInputName: String!
  itemQueryName: String!
  listOrderName: String!
  listQueryCountName: String!
  listQueryName: String!
  updateInputName: String!
  updateMutationName: String!
  updateManyInputName: String!
  updateManyMutationName: String!
  relateToOneForUpdateInputName: String!
  relateToManyForUpdateInputName: String!
  deleteMutationName: String!
  deleteManyMutationName: String!
}

type KeystoneAdminUISort {
  field: String!
  direction: KeystoneAdminUISortDirection!
}

enum KeystoneAdminUISortDirection {
  ASC
  DESC
}
